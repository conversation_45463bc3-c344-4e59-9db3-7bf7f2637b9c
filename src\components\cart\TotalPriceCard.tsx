import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {theme} from '../../constants';
import {useCartStore} from '../../modules/cart/store/cart-store';
import usePrices from '../../modules/cart/hooks/use-prices';
import useCurrency from '../../modules/catalog/hooks/use-currency';
import {useAppNavigation} from '../../hooks';
import {svg} from '../../assets/svg';

interface Props {
  containerStyle?: object;
  showOnlyWhenCartHasItems?: boolean;
}

const TotalPriceCard: React.FC<Props> = ({
  containerStyle,
  showOnlyWhenCartHasItems = true,
}) => {
  const {cartItems} = useCartStore((store) => store.state);
  const {total} = usePrices();
  const {currency} = useCurrency();
  const navigation = useAppNavigation();

  const handleCheckout = () => {
    navigation.navigate('Checkout');
  };

  if (showOnlyWhenCartHasItems && cartItems.length === 0) {
    return null;
  }

  return (
    <View
      style={{
        backgroundColor: theme.colors.white,
        borderTopWidth: 1,
        borderTopColor: theme.colors.lightBlue,
        paddingHorizontal: 20,
        paddingVertical: 16,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: -2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 5,
        ...containerStyle,
      }}
    >
      {/* Cart Info */}
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          gap: 12,
        }}
        onPress={handleCheckout}
      >
        <View
          style={{
            width: 24,
            height: 24,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <svg.BasketSvg />
        </View>
        <Text
          style={{
            ...theme.fonts.DMSans_700Bold,
            fontSize: 16,
            lineHeight: 16 * 1.3,
            color: theme.colors.mainColor,
          }}
        >
          Cart
        </Text>
      </TouchableOpacity>

      {/* Total Price */}
      <View style={{alignItems: 'flex-end'}}>
        <Text
          style={{
            ...theme.fonts.DMSans_400Regular,
            fontSize: 14,
            lineHeight: 14 * 1.3,
            color: theme.colors.textColor,
          }}
        >
          Total
        </Text>
        <Text
          style={{
            ...theme.fonts.DMSans_700Bold,
            fontSize: 16,
            lineHeight: 16 * 1.3,
            color: theme.colors.mainColor,
          }}
        >
          {total.toFixed(2)} {currency}
        </Text>
      </View>
    </View>
  );
};

export default TotalPriceCard;

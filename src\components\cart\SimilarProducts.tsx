import React from 'react';
import {View, Text, FlatList} from 'react-native';
import {theme} from '../../constants';
import {useCartStore} from '../../modules/cart/store/cart-store';
import useCartSimilarProducts from '../../modules/cart/hooks/use-similar-products';
import SimilarProduct from './SimilarProduct';
import {ProductType} from '../../modules/catalog/types/products';

interface Props {
  containerStyle?: object;
}

const SimilarProducts: React.FC<Props> = ({containerStyle}) => {
  const [cartItem] = useCartStore((store) => store.state.cartItems);
  const {products, productsAreLoading} = useCartSimilarProducts({
    limit: 8,
    similarProductSlug: cartItem ? cartItem.slug : '',
  });

  const renderProduct = ({item}: {item: ProductType}) => {
    return (
      <View>
        {item.items.map((productItem) => (
          <View
            key={productItem.id}
            style={{
              width: 140,
              marginRight: 12,
            }}
          >
            <SimilarProduct
              productItem={{
                slug: item.slug,
                id: productItem.id,
                productId: item.id,
                cartQuantity: 1,
                name: item.name,
                prices: productItem.prices,
                image: productItem.image,
                variations: productItem.variations,
                inStock: productItem.inStock,
              }}
            />
          </View>
        ))}
      </View>
    );
  };

  if (!products || productsAreLoading || products.length === 0) {
    return null;
  }

  return (
    <View
      style={{
        backgroundColor: theme.colors.white,
        paddingVertical: 20,
        ...containerStyle,
      }}
    >
      {/* Header */}
      <View style={{paddingHorizontal: 20, marginBottom: 16}}>
        <Text
          style={{
            ...theme.fonts.H4,
            color: theme.colors.mainColor,
            textAlign: 'center',
          }}
        >
          You may also like
        </Text>
      </View>

      {/* Products List */}
      <FlatList
        data={products}
        renderItem={(item) => renderProduct(item)}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          paddingHorizontal: 20,
        }}
        ItemSeparatorComponent={() => <View style={{width: 8}} />}
      />
    </View>
  );
};

export default SimilarProducts;

import {useEffect, useState} from 'react';

import {useQueryClient} from '@tanstack/react-query';
import useAuthRefresher from '../context/auth-refresher';
import {verifyAuthContent} from '../validation/dom-extraction-verification/auth';
import {UserSignUpType} from '../types';
import {signUp} from '../services/sign-up';
import {signIn} from '../services/sign-in';
import {useAppNavigation} from '../../../hooks';
import {
  getSignInStatusWarning,
  getSignUpStatusWarning,
} from '../utils/warnings/server-response-warning';
import {useCartStore} from '../../cart/store/cart-store';
import {addCartItemsOnServerSide} from '../../cart/services/cart-items-addition';

interface AuthWarnings {
  email: string;
  firstName?: string;
  lastName?: string;
  password: string;
  generalWarning: string;
}

export default function useAuth(auth: 'signIn' | 'signUp') {
  const [isLoading, setIsLoading] = useState(false);
  const {refreshUserAuthentication} = useAuthRefresher();
  const [password, setPassword] = useState('');
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const queryClient = useQueryClient();
  const [warning, setWarning] = useState<AuthWarnings>({
    email: '',
    firstName: '',
    lastName: '',
    password: '',
    generalWarning: '',
  });
  const navigation = useAppNavigation();
  const cartItems = useCartStore((store) => store.state.cartItems);

  function submitInfo() {
    console.log('🔐 Auth submission started for:', auth);
    const verificationResult = verifyAuthContent(auth, {
      email,
      password,
      firstName,
      lastName,
    });

    console.log('✅ Verification result:', verificationResult);

    setWarning(verificationResult.warning);

    //start sign in or sign up process on server side
    if (verificationResult.ok) {
      setIsLoading(true);

      if (auth == 'signUp') {
        signUp(verificationResult.data as UserSignUpType)
          .then((res) => {
            if (res.ok) {
              return signIn({
                email: verificationResult.data.email,
                password: verificationResult.data.password,
              });
            } else {
              const warning = getSignUpStatusWarning(res.status);
              setWarning({
                email: warning.email as string,
                password: '',
                generalWarning: warning.generalWarning,
              });
              setIsLoading(false);
              return null;
            }
          })
          .then((signInRes) => {
            if (signInRes && signInRes.ok) {
              //display the confirmation page
              //set email to be displayed in the confirmation page
              setEmail(verificationResult.data.email);

              //cart items addition to server
              const cartItemsToAdd = cartItems.map((cartItem) => ({
                id: cartItem.id,
                quantity: cartItem.cartQuantity,
              }));

              addCartItemsOnServerSide(cartItemsToAdd).catch((error) => {
                console.log('Cart items addition error:', error);
              });

              //refresh user authentication state
              refreshUserAuthentication();
              queryClient.invalidateQueries({queryKey: ['user-data']});

              setIsLoading(false);
              // Navigate safely - check if TabNavigator is available
              try {
                navigation.navigate('TabNavigator');
              } catch (navError) {
                console.log('Navigation error:', navError);
                // Fallback navigation or refresh the auth state
                refreshUserAuthentication();
              }
            } else if (signInRes && !signInRes.ok) {
              const warning = getSignInStatusWarning(signInRes.status);
              setWarning({
                email: '',
                password: '',
                generalWarning: warning,
              });
              setIsLoading(false);
            }
          })
          .catch((error) => {
            console.log('Sign up error:', error);
            setWarning({
              email: '',
              password: '',
              generalWarning: 'An unexpected error occurred. Please try again.',
            });
            setIsLoading(false);
          });
      } else {
        signIn(verificationResult.data)
          .then((res) => {
            if (res.ok) {
              // push cart items from locale storage to the server
              // redirect user to home page

              //cart items addition to server and deletion from localstorage if we've cart app

              //refresh user authentication state
              refreshUserAuthentication();

              queryClient.invalidateQueries({queryKey: ['user-data']});

              // Navigate safely - check if TabNavigator is available
              try {
                navigation.navigate('TabNavigator');
              } catch (navError) {
                console.log('Navigation error:', navError);
                // Fallback navigation or refresh the auth state
                refreshUserAuthentication();
              }
            } else {
              const warning = getSignInStatusWarning(res.status);
              setWarning({
                email: '',
                password: '',
                generalWarning: warning,
              });
            }

            setIsLoading(false);
          })
          .catch((error) => {
            console.log('Sign in error:', error);
            setWarning({
              email: '',
              password: '',
              generalWarning: 'An unexpected error occurred. Please try again.',
            });
            setIsLoading(false);
          });
      }
    }
  }

  //in case we change from sign in to sign up
  useEffect(() => {
    setWarning({
      email: '',
      firstName: '',
      lastName: '',
      password: '',
      generalWarning: '',
    });
  }, [auth]);

  return {
    warning,
    submitInfo,
    isLoading,
    firstName,
    setFirstName,
    lastName,
    setLastName,
    confirmPassword,
    setConfirmPassword,
    password,
    email,
    setPassword,
    setEmail,
  };
}

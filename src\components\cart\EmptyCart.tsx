import React from 'react';
import {View, Text} from 'react-native';
import {theme} from '../../constants';
import {useAppNavigation} from '../../hooks';
import Button from '../buttons/Button';
import {svg} from '../../assets/svg';

const EmptyCart: React.FC = () => {
  const navigation = useAppNavigation();

  const handleShopNow = () => {
    navigation.navigate('Shop', {title: 'Shop', products: []});
  };

  return (
    <View
      style={{
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 24,
        paddingVertical: 32,
      }}
    >
      {/* Empty Cart Icon */}
      <View
        style={{
          width: 120,
          height: 120,
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: 32,
        }}
      >
        <svg.ShoppingBagSvg />
      </View>

      <View style={{alignItems: 'center', marginBottom: 32}}>
        <Text
          style={{
            ...theme.fonts.H3,
            color: theme.colors.mainColor,
            textAlign: 'center',
            marginBottom: 12,
          }}
        >
          Your cart is empty!
        </Text>
        <Text
          style={{
            ...theme.fonts.textStyle_16,
            color: theme.colors.textColor,
            textAlign: 'center',
            maxWidth: 280,
            lineHeight: 24,
          }}
        >
          Looks like you haven't added anything to your cart yet. Start shopping
          to fill it up!
        </Text>
      </View>

      <Button
        title='Start Shopping'
        onPress={handleShopNow}
        containerStyle={{
          width: '100%',
          maxWidth: 280,
        }}
      />
    </View>
  );
};

export default EmptyCart;

import {AxiosError} from 'axios';
import extractJWTokens from '../utils/jwt/extract-tokens';
import {GET} from '../../../lib/http-methods';
import {UserDataType, UserResponseDataType} from '../types';
import {castToUserType} from '../utils/data-utils/types-casting/user';
import {refreshToken} from './refresh-token';

export async function retrieveUserDetails(): Promise<UserDataType | null> {
  const {access} = await extractJWTokens();

  // If no access token, return null immediately
  if (!access) {
    console.log('No access token found');
    return null;
  }

  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET(`/users/me`, header);
    const userData = res.data as UserResponseDataType;

    return castToUserType(userData);
  } catch (error) {
    const axiosError = error as AxiosError;
    console.log('User details extraction error:', axiosError.response?.status);

    if (axiosError.response?.status === 401) {
      try {
        const res = await refreshToken(retrieveUserDetails);
        return res;
      } catch (refreshError) {
        console.log('Token refresh failed:', refreshError);
        return null;
      }
    }

    return null;
  }
}

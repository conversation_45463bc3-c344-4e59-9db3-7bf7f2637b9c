import React from 'react';
import {View, ScrollView} from 'react-native';

import {components} from '../../components';
import {theme, tabs} from '../../constants';
import {useAppNavigation} from '../../hooks';
import {useCart} from '../../modules/cart/store/cart-store';

const Order = () => {
  const {cartItems, isLoading} = useCart();
  const navigation = useAppNavigation();
  const total = cartItems
    .map((item) => item.cartQuantity * item.prices[0].promotionalPrice)
    .reduce((a, b) => a + b, 0);
  const products = cartItems;

  // Show loader only when cart is empty AND we're loading recommendations
  if (cartItems.length === 0 && isLoading) {
    return <components.Loader />;
  }

  const renderStatusBar = (): JSX.Element => {
    return (
      <components.StatusBar
        backgroundColor={theme.colors.transparent}
        barStyle='dark-content'
      />
    );
  };

  const renderHeader = (): JSX.Element => {
    return (
      <components.Header
        burgerIcon={true}
        basket={true}
        bottomLine={true}
        title={cartItems.length === 0 ? 'Cart' : ''}
      />
    );
  };

  const renderTabBar = (): JSX.Element => {
    return (
      <components.TabBar>
        {tabs.map((item, index) => {
          return <components.TabBarItem item={item} key={index} />;
        })}
        <components.FreeShippingProgress />
      </components.TabBar>
    );
  };

  const renderProducts = () => {
    return (
      <View style={{marginLeft: 20}}>
        {cartItems.map((item, index, array) => {
          return <components.CartProductContainer productItem={item} />;
        })}
      </View>
    );
  };

  const renderEmptyCart = () => {
    return (
      <React.Fragment>
        <components.EmptyCart />
      </React.Fragment>
    );
  };

  const renderTotal = () => {
    return (
      <components.Container
        containerStyle={{
          marginHorizontal: 20,
        }}
      >
        <components.ContainerLine />
        <components.ContainerItem
          title='Total'
          price={`$${total}`}
          containerStyle={{
            marginBottom: 0,
          }}
          titleStyle={{
            ...theme.fonts.H4,
            color: theme.colors.mainColor,
          }}
          priceStyle={{
            ...theme.fonts.H4,
            color: theme.colors.mainColor,
          }}
        />
      </components.Container>
    );
  };

  const renderVoucher = () => {
    return (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 50,
          marginHorizontal: 20,
        }}
      >
        <components.InputField
          placeholder='Promocode'
          label='Enter the voucher'
          containerStyle={{flex: 1, marginRight: 10}}
        />
        <View style={{width: '30%'}}>
          <components.Button
            title='+ add'
            onPress={() => {
              console.log('add');
            }}
          />
        </View>
      </View>
    );
  };

  const renderContent = (): JSX.Element => {
    return (
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
          paddingVertical: 20,
          paddingBottom: 20,
          paddingHorizontal: cartItems.length === 0 ? 20 : 0,
          justifyContent: cartItems.length === 0 ? 'center' : 'flex-start',
        }}
      >
        {cartItems.length === 0 ? renderEmptyCart() : renderProducts()}
        {cartItems.length !== 0 && renderVoucher()}
        {cartItems.length !== 0 && renderTotal()}
      </ScrollView>
    );
  };

  const renderButton = (): JSX.Element => {
    return (
      <View style={{padding: 20}}>
        <components.Button
          title={'proceed to checkout'}
          onPress={() => {
            if (cartItems.length !== 0) {
              navigation.navigate('Checkout');
            }
          }}
          transparent={true}
        />
      </View>
    );
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderButton()}
      {renderTabBar()}
    </components.SmartView>
  );
};

export default Order;

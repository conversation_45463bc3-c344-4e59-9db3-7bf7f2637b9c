import React, {useEffect, useState} from 'react';
import {View, Text, Image, TouchableOpacity} from 'react-native';
import {theme} from '../../constants';
import {ProductItemType} from '../../modules/cart/types/products';
import {useCartStore} from '../../modules/cart/store/cart-store';
import {formatPrice} from '../../modules/catalog/utils/prices-transformation';
import useCurrency from '../../modules/catalog/hooks/use-currency';
import {svg} from '../../assets/svg';

interface Props {
  productItem: ProductItemType;
}

const SimilarProduct: React.FC<Props> = ({productItem}) => {
  const [productImage, setProductImage] = useState(
    '/not-found/product-image.webp',
  );

  const {addProductItem} = useCartStore((store) => store.actions);
  const {currency} = useCurrency();

  useEffect(() => {
    if (productItem) setProductImage(productItem.image);
  }, [productItem]);

  const handleAddToCart = () => {
    addProductItem({
      slug: productItem.slug,
      id: productItem.id,
      productId: productItem.productId,
      cartQuantity: 1,
      name: productItem.name,
      prices: productItem.prices,
      image: productItem.image,
      variations: productItem.variations,
      inStock: productItem.inStock,
    });
  };

  return (
    <View
      style={{
        backgroundColor: theme.colors.white,
        borderRadius: 12,
        padding: 8,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 1,
        },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
        height: 200,
      }}
    >
      {/* Product Image */}
      <View
        style={{
          position: 'relative',
          aspectRatio: 1,
          backgroundColor: theme.colors.imageBackground,
          borderRadius: 8,
          overflow: 'hidden',
          marginBottom: 8,
        }}
      >
        <Image
          source={{uri: productImage}}
          style={{
            width: '100%',
            height: '100%',
          }}
          resizeMode="cover"
          onError={() => setProductImage('/not-found/product-image.webp')}
        />
        <TouchableOpacity
          style={{
            position: 'absolute',
            top: 8,
            right: 8,
            width: 32,
            height: 32,
            borderRadius: 16,
            backgroundColor: theme.colors.mainColor,
            justifyContent: 'center',
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.2,
            shadowRadius: 3,
            elevation: 3,
          }}
          onPress={handleAddToCart}
        >
          <svg.PlusSvg />
        </TouchableOpacity>
      </View>

      {/* Product Name */}
      <View style={{flex: 1, justifyContent: 'center', paddingHorizontal: 4}}>
        <Text
          style={{
            ...theme.fonts.DMSans_500Medium,
            fontSize: 12,
            lineHeight: 12 * 1.4,
            color: theme.colors.mainColor,
            textAlign: 'center',
          }}
          numberOfLines={2}
        >
          {productItem.name}
        </Text>
      </View>

      {/* Product Price */}
      <View style={{alignItems: 'center', paddingBottom: 4}}>
        <Text
          style={{
            ...theme.fonts.DMSans_700Bold,
            fontSize: 14,
            lineHeight: 14 * 1.3,
            color: theme.colors.mainColor,
          }}
        >
          {formatPrice(productItem.prices[0].promotionalPrice)} {currency}
        </Text>
      </View>
    </View>
  );
};

export default SimilarProduct;

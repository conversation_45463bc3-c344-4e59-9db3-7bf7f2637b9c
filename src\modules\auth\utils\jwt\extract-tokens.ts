import {getItemAsync, setItemAsync} from 'expo-secure-store';

export default async function extractJWTokens() {
  let access = await getItemAsync('access');
  let refresh = await getItemAsync('refresh');

  // For React Native, we only use secure storage
  // Cookie handling is only for web environments
  if (typeof document !== 'undefined' && !(access && refresh)) {
    try {
      //checking if we've tokens inside the cookie (web only)
      const cookies = document.cookie.split('; ');
      const accessCookieValue = cookies.find((cookie) =>
        cookie.startsWith('x-At='),
      );

      const refreshCookieValue = cookies.find((cookie) =>
        cookie.startsWith('x-Rt='),
      );

      if (accessCookieValue && refreshCookieValue) {
        access = accessCookieValue.split('=')[1];
        refresh = refreshCookieValue.split('=')[1];

        //cookies deletion
        const d = new Date();
        document.cookie = `x-At=; Path=/; expires=${d.toUTCString()}`;
        document.cookie = `x-Rt=; Path=/; expires=${d.toUTCString()}`;

        // Store in secure storage for React Native
        await setItemAsync('access', access);
        await setItemAsync('refresh', refresh);
      }
    } catch (error) {
      console.log('Cookie extraction error (expected in React Native):', error);
    }
  }

  return {
    access,
    refresh,
  };
}

import React from 'react';
import {View, Text} from 'react-native';
import {theme} from '../../constants';
import usePrices from '../../modules/cart/hooks/use-prices';
import calculateFreeShippingProgress from '../../modules/cart/utils/free-shipping-progress-calculation';
import useCurrency from '../../modules/catalog/hooks/use-currency';
import {svg} from '../../assets/svg';

interface Props {
  containerStyle?: object;
  showIcon?: boolean;
}

const FreeShippingProgress: React.FC<Props> = ({
  containerStyle,
  showIcon = true,
}) => {
  const {subtotal, minAmountForFreeShipping} = usePrices();
  const {currency} = useCurrency();
  const freeShippingProgress = calculateFreeShippingProgress(
    minAmountForFreeShipping,
    subtotal,
  );

  const investAmount = minAmountForFreeShipping - subtotal;
  const isEligibleForFreeShipping = investAmount <= 0;

  return (
    <View
      style={{
        backgroundColor: theme.colors.white,
        borderRadius: 8,
        padding: 16,
        marginVertical: 8,
        ...containerStyle,
      }}
    >
      {/* Message */}
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          gap: 8,
          marginBottom: 12,
        }}
      >
        {showIcon && (
          <View
            style={{
              width: 24,
              height: 24,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <svg.ShippingMapSvg />
          </View>
        )}
        <Text
          style={{
            ...theme.fonts.DMSans_500Medium,
            fontSize: 14,
            lineHeight: 14 * 1.4,
            color: theme.colors.mainColor,
            flex: 1,
          }}
        >
          {isEligibleForFreeShipping
            ? 'Congratulations! You qualify for free shipping!'
            : `Add ${investAmount.toFixed(2)} ${currency} more for free shipping`}
        </Text>
      </View>

      {/* Progress Bar */}
      <View
        style={{
          height: 6,
          backgroundColor: '#E5E5E5',
          borderRadius: 3,
          overflow: 'hidden',
        }}
      >
        <View
          style={{
            height: '100%',
            width: `${freeShippingProgress}%`,
            backgroundColor: isEligibleForFreeShipping
              ? '#10B981'
              : theme.colors.mainColor,
            borderRadius: 3,
          }}
        />
      </View>

      {/* Progress Text */}
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginTop: 8,
        }}
      >
        <Text
          style={{
            ...theme.fonts.DMSans_400Regular,
            fontSize: 12,
            color: theme.colors.textColor,
          }}
        >
          {subtotal.toFixed(2)} {currency}
        </Text>
        <Text
          style={{
            ...theme.fonts.DMSans_400Regular,
            fontSize: 12,
            color: theme.colors.textColor,
          }}
        >
          {minAmountForFreeShipping.toFixed(2)} {currency}
        </Text>
      </View>
    </View>
  );
};

export default FreeShippingProgress;
